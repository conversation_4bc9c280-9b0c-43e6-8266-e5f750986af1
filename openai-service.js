/**
 * OpenAI API Service for Resume Assistant
 * Handles communication with OpenAI API for generating job application responses
 */

class OpenAIService {
  constructor() {
    this.apiKey = null;
    this.baseURL = "https://api.openai.com/v1";
    this.model = "gpt-3.5-turbo";
  }

  /**
   * Initialize the service with API key
   * @param {string} apiKey - OpenAI API key
   */
  async initialize(apiKey) {
    this.apiKey = apiKey;

    // Store API key securely in Chrome storage
    await chrome.storage.local.set({ openai_api_key: apiKey });
  }

  /**
   * Load API key from storage
   */
  async loadApiKey() {
    const result = await chrome.storage.local.get(["openai_api_key"]);
    this.apiKey = result.openai_api_key;
    return this.apiKey;
  }

  /**
   * Check if API key is configured
   */
  isConfigured() {
    return !!this.apiKey;
  }

  /**
   * Generate response for job application question
   * @param {string} question - The job application question
   * @param {string} resumeContent - The resume content in the selected language
   * @param {string} language - Language code ('pt' or 'en')
   * @param {Object} careerDetails - Additional career details for context
   * @param {string} jobUrl - Optional job posting URL for additional context
   * @returns {Promise<string>} Generated response
   */
  async generateResponse(
    question,
    resumeContent,
    language,
    careerDetails = null,
    jobUrl = null
  ) {
    if (!this.apiKey) {
      throw new Error("OpenAI API key not configured");
    }

    const systemPrompt = this.getSystemPrompt(language);
    const userPrompt = this.getUserPrompt(
      question,
      resumeContent,
      language,
      careerDetails,
      jobUrl
    );

    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${this.apiKey}`,
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            { role: "system", content: systemPrompt },
            { role: "user", content: userPrompt },
          ],
          max_tokens: 200,
          temperature: 0.7,
          top_p: 1,
          frequency_penalty: 0,
          presence_penalty: 0,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(
          `OpenAI API Error: ${errorData.error?.message || response.statusText}`
        );
      }

      const data = await response.json();
      const generatedText = data.choices[0]?.message?.content?.trim();

      if (!generatedText) {
        throw new Error("No response generated from OpenAI");
      }

      // Ensure response meets requirements (max 5 lines, no bullet points)
      return this.formatResponse(generatedText, language);
    } catch (error) {
      console.error("OpenAI API Error:", error);
      throw error;
    }
  }

  /**
   * Get system prompt based on language
   * @param {string} language - Language code
   * @returns {string} System prompt
   */
  getSystemPrompt(language) {
    const prompts = {
      pt: `Você é um assistente especializado em criar respostas para formulários de candidatura a empregos. 
           Suas respostas devem ser:
           - Máximo de 5 linhas
           - Sem bullet points ou listas
           - Diretas e profissionais
           - Baseadas apenas nas informações do currículo fornecido
           - Em português brasileiro
           - Adequadas para campos de texto em formulários de candidatura
           - Focadas na experiência e qualificações relevantes para a pergunta`,

      en: `You are an assistant specialized in creating responses for job application forms.
           Your responses must be:
           - Maximum 5 lines
           - No bullet points or lists
           - Direct and professional
           - Based only on the resume information provided
           - In English
           - Suitable for text fields in job application forms
           - Focused on experience and qualifications relevant to the question`,
    };

    return prompts[language] || prompts.en;
  }

  /**
   * Get user prompt with question and resume content
   * @param {string} question - Job application question
   * @param {string} resumeContent - Resume content
   * @param {string} language - Language code
   * @param {Object} careerDetails - Additional career details
   * @param {string} jobUrl - Optional job posting URL
   * @returns {string} User prompt
   */
  getUserPrompt(
    question,
    resumeContent,
    language,
    careerDetails = null,
    jobUrl = null
  ) {
    let careerContext = "";

    if (careerDetails && careerDetails[language]) {
      const details = careerDetails[language];
      const contextParts = [];

      if (details.challenge && details.challenge.trim()) {
        contextParts.push(
          language === "pt"
            ? `Maior desafio da carreira: ${details.challenge}`
            : `Biggest career challenge: ${details.challenge}`
        );
      }

      if (details.motivation && details.motivation.trim()) {
        contextParts.push(
          language === "pt"
            ? `Por que estou procurando outro emprego: ${details.motivation}`
            : `Why I'm looking for a new job: ${details.motivation}`
        );
      }

      if (details.proudProject && details.proudProject.trim()) {
        contextParts.push(
          language === "pt"
            ? `Projeto que tenho mais orgulho: ${details.proudProject}`
            : `Project I'm most proud of: ${details.proudProject}`
        );
      }

      if (contextParts.length > 0) {
        careerContext =
          "\n\n" +
          (language === "pt"
            ? "Informações adicionais sobre minha carreira:\n"
            : "Additional career information:\n") +
          contextParts.join("\n\n");
      }
    }

    // Add job URL context if provided
    let jobContext = "";
    if (jobUrl && jobUrl.trim()) {
      jobContext =
        "\n\n" +
        (language === "pt"
          ? `URL da vaga: ${jobUrl}\n\nPor favor, considere as informações desta vaga específica ao gerar a resposta. Se possível, analise o conteúdo da vaga para tornar a resposta mais direcionada e relevante.`
          : `Job URL: ${jobUrl}\n\nPlease consider the information from this specific job posting when generating the response. If possible, analyze the job posting content to make the response more targeted and relevant.`);
    }

    const templates = {
      pt: `Pergunta da candidatura: "${question}"

Conteúdo do meu currículo:
${resumeContent}${careerContext}${jobContext}

Por favor, responda à pergunta da candidatura baseando-se nas informações do meu currículo, detalhes da carreira e informações da vaga fornecidos. A resposta deve ser concisa (máximo 5 linhas), sem bullet points, e adequada para um campo de texto em formulário de candidatura. Use todas as informações disponíveis para tornar a resposta mais personalizada, convincente e direcionada para esta vaga específica.`,

      en: `Job application question: "${question}"

My resume content:
${resumeContent}${careerContext}${jobContext}

Please answer the job application question based on the information in my resume, career details, and job posting provided. The response should be concise (maximum 5 lines), without bullet points, and suitable for a text field in a job application form. Use all available information to make the response more personalized, compelling, and targeted to this specific job opportunity.`,
    };

    return templates[language] || templates.en;
  }

  /**
   * Format response to meet requirements
   * @param {string} response - Raw response from OpenAI
   * @param {string} language - Language code
   * @returns {string} Formatted response
   */
  formatResponse(response, language) {
    // Remove bullet points and list markers
    let formatted = response
      .replace(/^[\s]*[-•*]\s*/gm, "")
      .replace(/^\d+\.\s*/gm, "")
      .replace(/^[\s]*[→▶►]\s*/gm, "");

    // Split into lines and limit to 5 lines
    const lines = formatted
      .split("\n")
      .filter((line) => line.trim().length > 0);

    if (lines.length > 5) {
      formatted = lines.slice(0, 5).join(" ");
    } else {
      formatted = lines.join(" ");
    }

    // Clean up extra spaces and ensure proper sentence structure
    formatted = formatted.replace(/\s+/g, " ").trim();

    // Ensure it ends with proper punctuation
    if (formatted && !formatted.match(/[.!?]$/)) {
      formatted += ".";
    }

    return formatted;
  }

  /**
   * Test API connection
   * @returns {Promise<boolean>} True if connection successful
   */
  async testConnection() {
    if (!this.apiKey) {
      throw new Error("API key not configured");
    }

    try {
      const response = await fetch(`${this.baseURL}/models`, {
        headers: {
          Authorization: `Bearer ${this.apiKey}`,
        },
      });

      return response.ok;
    } catch (error) {
      console.error("API connection test failed:", error);
      return false;
    }
  }
}

// Export for use in other files
if (typeof module !== "undefined" && module.exports) {
  module.exports = OpenAIService;
} else {
  window.OpenAIService = OpenAIService;
}
