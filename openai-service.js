/**
 * OpenAI API Service for Resume Assistant
 * Handles communication with OpenAI API for generating job application responses
 */

class OpenAIService {
  constructor() {
    this.apiKey = null;
    this.baseURL = 'https://api.openai.com/v1';
    this.model = 'gpt-3.5-turbo';
  }

  /**
   * Initialize the service with API key
   * @param {string} apiKey - OpenAI API key
   */
  async initialize(apiKey) {
    this.apiKey = apiKey;
    
    // Store API key securely in Chrome storage
    await chrome.storage.local.set({ openai_api_key: apiKey });
  }

  /**
   * Load API key from storage
   */
  async loadApiKey() {
    const result = await chrome.storage.local.get(['openai_api_key']);
    this.apiKey = result.openai_api_key;
    return this.apiKey;
  }

  /**
   * Check if API key is configured
   */
  isConfigured() {
    return !!this.apiKey;
  }

  /**
   * Generate response for job application question
   * @param {string} question - The job application question
   * @param {string} resumeContent - The resume content in the selected language
   * @param {string} language - Language code ('pt' or 'en')
   * @returns {Promise<string>} Generated response
   */
  async generateResponse(question, resumeContent, language) {
    if (!this.apiKey) {
      throw new Error('OpenAI API key not configured');
    }

    const systemPrompt = this.getSystemPrompt(language);
    const userPrompt = this.getUserPrompt(question, resumeContent, language);

    try {
      const response = await fetch(`${this.baseURL}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${this.apiKey}`
        },
        body: JSON.stringify({
          model: this.model,
          messages: [
            { role: 'system', content: systemPrompt },
            { role: 'user', content: userPrompt }
          ],
          max_tokens: 200,
          temperature: 0.7,
          top_p: 1,
          frequency_penalty: 0,
          presence_penalty: 0
        })
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(`OpenAI API Error: ${errorData.error?.message || response.statusText}`);
      }

      const data = await response.json();
      const generatedText = data.choices[0]?.message?.content?.trim();

      if (!generatedText) {
        throw new Error('No response generated from OpenAI');
      }

      // Ensure response meets requirements (max 5 lines, no bullet points)
      return this.formatResponse(generatedText, language);

    } catch (error) {
      console.error('OpenAI API Error:', error);
      throw error;
    }
  }

  /**
   * Get system prompt based on language
   * @param {string} language - Language code
   * @returns {string} System prompt
   */
  getSystemPrompt(language) {
    const prompts = {
      pt: `Você é um assistente especializado em criar respostas para formulários de candidatura a empregos. 
           Suas respostas devem ser:
           - Máximo de 5 linhas
           - Sem bullet points ou listas
           - Diretas e profissionais
           - Baseadas apenas nas informações do currículo fornecido
           - Em português brasileiro
           - Adequadas para campos de texto em formulários de candidatura
           - Focadas na experiência e qualificações relevantes para a pergunta`,

      en: `You are an assistant specialized in creating responses for job application forms.
           Your responses must be:
           - Maximum 5 lines
           - No bullet points or lists
           - Direct and professional
           - Based only on the resume information provided
           - In English
           - Suitable for text fields in job application forms
           - Focused on experience and qualifications relevant to the question`
    };

    return prompts[language] || prompts.en;
  }

  /**
   * Get user prompt with question and resume content
   * @param {string} question - Job application question
   * @param {string} resumeContent - Resume content
   * @param {string} language - Language code
   * @returns {string} User prompt
   */
  getUserPrompt(question, resumeContent, language) {
    const templates = {
      pt: `Pergunta da candidatura: "${question}"

Conteúdo do meu currículo:
${resumeContent}

Por favor, responda à pergunta da candidatura baseando-se apenas nas informações do meu currículo. A resposta deve ser concisa (máximo 5 linhas), sem bullet points, e adequada para um campo de texto em formulário de candidatura.`,

      en: `Job application question: "${question}"

My resume content:
${resumeContent}

Please answer the job application question based only on the information in my resume. The response should be concise (maximum 5 lines), without bullet points, and suitable for a text field in a job application form.`
    };

    return templates[language] || templates.en;
  }

  /**
   * Format response to meet requirements
   * @param {string} response - Raw response from OpenAI
   * @param {string} language - Language code
   * @returns {string} Formatted response
   */
  formatResponse(response, language) {
    // Remove bullet points and list markers
    let formatted = response
      .replace(/^[\s]*[-•*]\s*/gm, '')
      .replace(/^\d+\.\s*/gm, '')
      .replace(/^[\s]*[→▶►]\s*/gm, '');

    // Split into lines and limit to 5 lines
    const lines = formatted.split('\n').filter(line => line.trim().length > 0);
    
    if (lines.length > 5) {
      formatted = lines.slice(0, 5).join(' ');
    } else {
      formatted = lines.join(' ');
    }

    // Clean up extra spaces and ensure proper sentence structure
    formatted = formatted
      .replace(/\s+/g, ' ')
      .trim();

    // Ensure it ends with proper punctuation
    if (formatted && !formatted.match(/[.!?]$/)) {
      formatted += '.';
    }

    return formatted;
  }

  /**
   * Test API connection
   * @returns {Promise<boolean>} True if connection successful
   */
  async testConnection() {
    if (!this.apiKey) {
      throw new Error('API key not configured');
    }

    try {
      const response = await fetch(`${this.baseURL}/models`, {
        headers: {
          'Authorization': `Bearer ${this.apiKey}`
        }
      });

      return response.ok;
    } catch (error) {
      console.error('API connection test failed:', error);
      return false;
    }
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = OpenAIService;
} else {
  window.OpenAIService = OpenAIService;
}
