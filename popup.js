// Import OpenAI service
const openAIService = new OpenAIService();

document.addEventListener("DOMContentLoaded", async function () {
  // DOM elements
  const questionInput = document.getElementById("question");
  const responderButton = document.getElementById("responderButton");
  const responseSection = document.getElementById("responseSection");
  const responseText = document.getElementById("responseText");
  const copyButton = document.getElementById("copyButton");
  const clearButton = document.getElementById("clearButton");
  const loadingSpinner = document.getElementById("loadingSpinner");
  const buttonText = document.getElementById("buttonText");

  // Language tabs
  const tabButtons = document.querySelectorAll(".tab-button");

  // Text elements for i18n
  const questionLabelText = document.getElementById("questionLabelText");
  const responseLabelText = document.getElementById("responseLabelText");
  const copyButtonText = document.getElementById("copyButtonText");
  const clearButtonText = document.getElementById("clearButtonText");
  const settingsLinkText = document.getElementById("settingsLinkText");

  // Current state
  let currentLanguage = "pt";
  let isProcessing = false;

  // Initialize OpenAI service
  await openAIService.loadApiKey();

  // Load saved language preference
  const result = await chrome.storage.local.get(["preferredLanguage"]);
  if (
    result.preferredLanguage &&
    ["pt", "en"].includes(result.preferredLanguage)
  ) {
    currentLanguage = result.preferredLanguage;
  }

  // Initialize UI
  updateLanguage(currentLanguage);
  updateTabSelection(currentLanguage);

  // Event listeners
  responderButton.addEventListener("click", handleQuestion);
  copyButton.addEventListener("click", copyResponse);
  clearButton.addEventListener("click", clearResponse);

  questionInput.addEventListener("keypress", function (e) {
    if (e.key === "Enter" && e.ctrlKey) {
      e.preventDefault();
      handleQuestion();
    }
  });

  // Language tab event listeners
  tabButtons.forEach((button) => {
    button.addEventListener("click", function () {
      const lang = this.dataset.lang;
      if (lang !== currentLanguage) {
        currentLanguage = lang;
        updateLanguage(lang);
        updateTabSelection(lang);
        saveLanguagePreference(lang);
      }
    });
  });

  async function handleQuestion() {
    const question = questionInput.value.trim();
    if (!question) {
      showError(getLocalizedText("enterQuestion"));
      return;
    }

    if (isProcessing) return;

    // Check if OpenAI API key is configured
    if (!openAIService.isConfigured()) {
      showError(getLocalizedText("apiKeyNotConfigured"));
      return;
    }

    isProcessing = true;
    setLoadingState(true);

    try {
      // Get stored resumes
      const result = await chrome.storage.local.get(["resumes"]);
      const resumes = result.resumes || {};

      if (
        !resumes[currentLanguage] ||
        resumes[currentLanguage].trim().length === 0
      ) {
        showError(getLocalizedText("noResumeFound"));
        return;
      }

      // Generate response using OpenAI
      const response = await openAIService.generateResponse(
        question,
        resumes[currentLanguage],
        currentLanguage
      );

      showResponse(response);
    } catch (error) {
      console.error("Error processing question:", error);
      showError(getLocalizedText("processingError") + ": " + error.message);
    } finally {
      isProcessing = false;
      setLoadingState(false);
    }
  }

  function setLoadingState(loading) {
    responderButton.disabled = loading;
    if (loading) {
      buttonText.style.display = "none";
      loadingSpinner.classList.remove("hidden");
    } else {
      buttonText.style.display = "inline";
      loadingSpinner.classList.add("hidden");
    }
  }

  function showResponse(response) {
    responseText.value = response;
    responseText.style.color = "#2c3e50";
    responseSection.classList.remove("hidden");

    // Scroll to response
    responseSection.scrollIntoView({ behavior: "smooth", block: "nearest" });
  }

  function showError(message) {
    responseText.value = message;
    responseText.style.color = "#e74c3c";
    responseSection.classList.remove("hidden");
  }

  function copyResponse() {
    responseText.select();
    document.execCommand("copy");

    // Show feedback
    const originalText = copyButtonText.textContent;
    copyButtonText.textContent = getLocalizedText("copied");
    setTimeout(() => {
      copyButtonText.textContent = originalText;
    }, 1500);
  }

  function clearResponse() {
    responseText.value = "";
    responseText.style.color = "#2c3e50";
    responseSection.classList.add("hidden");
    questionInput.focus();
  }

  function updateTabSelection(lang) {
    tabButtons.forEach((button) => {
      if (button.dataset.lang === lang) {
        button.classList.add("active");
      } else {
        button.classList.remove("active");
      }
    });
  }

  function updateLanguage(lang) {
    const texts = getLocalizedTexts(lang);

    questionLabelText.textContent = texts.questionLabel;
    questionInput.placeholder = texts.questionPlaceholder;
    buttonText.textContent = texts.responderButton;
    responseLabelText.textContent = texts.responseLabel;
    copyButtonText.textContent = texts.copyButton;
    clearButtonText.textContent = texts.clearButton;
    settingsLinkText.textContent = texts.settingsLink;
  }

  function saveLanguagePreference(lang) {
    chrome.storage.local.set({ preferredLanguage: lang });
  }

  function getLocalizedText(key) {
    return getLocalizedTexts(currentLanguage)[key];
  }

  function getLocalizedTexts(lang) {
    const texts = {
      pt: {
        questionLabel: "Digite sua pergunta sobre a vaga:",
        questionPlaceholder:
          "Ex: Descreva sua experiência com desenvolvimento web...",
        responderButton: "Responder",
        responseLabel: "Resposta gerada:",
        copyButton: "Copiar",
        clearButton: "Limpar",
        settingsLink: "⚙️ Gerenciar Currículos",
        enterQuestion: "Por favor, digite uma pergunta.",
        apiKeyNotConfigured:
          "Chave da API OpenAI não configurada. Configure nas opções.",
        noResumeFound:
          "Currículo não encontrado para este idioma. Adicione na página de opções.",
        processingError: "Erro ao processar a pergunta",
        copied: "Copiado!",
      },
      en: {
        questionLabel: "Enter your job application question:",
        questionPlaceholder:
          "Ex: Describe your experience with web development...",
        responderButton: "Answer",
        responseLabel: "Generated response:",
        copyButton: "Copy",
        clearButton: "Clear",
        settingsLink: "⚙️ Manage Resumes",
        enterQuestion: "Please enter a question.",
        apiKeyNotConfigured:
          "OpenAI API key not configured. Set it up in options.",
        noResumeFound:
          "Resume not found for this language. Add it in the options page.",
        processingError: "Error processing question",
        copied: "Copied!",
      },
    };

    return texts[lang] || texts.en;
  }
});
