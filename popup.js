document.addEventListener('DOMContentLoaded', function() {
  const questionInput = document.getElementById('question');
  const askButton = document.getElementById('askButton');
  const responseArea = document.getElementById('response');
  const langSelect = document.getElementById('lang');

  // Load saved language preference
  chrome.storage.local.get(['preferredLanguage'], function(result) {
    if (result.preferredLanguage) {
      langSelect.value = result.preferredLanguage;
    }
  });

  // Save language preference when changed
  langSelect.addEventListener('change', function() {
    chrome.storage.local.set({
      preferredLanguage: langSelect.value
    });
  });

  askButton.addEventListener('click', handleQuestion);
  questionInput.addEventListener('keypress', function(e) {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleQuestion();
    }
  });

  async function handleQuestion() {
    const question = questionInput.value.trim();
    if (!question) {
      showError('Por favor, digite uma pergunta.');
      return;
    }

    askButton.disabled = true;
    responseArea.innerHTML = '<div class="loading">Processando pergunta...</div>';

    try {
      // Get stored resumes
      const result = await chrome.storage.local.get(['resumes']);
      const resumes = result.resumes || {};

      if (Object.keys(resumes).length === 0) {
        showError('Nenhum currículo encontrado. Por favor, adicione seus currículos na página de opções.');
        return;
      }

      // Detect language or use selected language
      const selectedLang = langSelect.value;
      const detectedLang = selectedLang === 'auto' ? detectLanguage(question) : selectedLang;

      // Process the question
      const response = await processQuestion(question, resumes, detectedLang);
      showResponse(response);

    } catch (error) {
      showError('Erro ao processar a pergunta: ' + error.message);
    } finally {
      askButton.disabled = false;
    }
  }

  function detectLanguage(text) {
    // Simple language detection based on common words
    const portugueseWords = ['o', 'a', 'de', 'que', 'e', 'do', 'da', 'em', 'um', 'para', 'é', 'com', 'não', 'uma', 'os', 'no', 'se', 'na', 'por', 'mais', 'as', 'dos', 'como', 'mas', 'foi', 'ao', 'ele', 'das', 'tem', 'à', 'seu', 'sua', 'ou', 'ser', 'quando', 'muito', 'há', 'nos', 'já', 'está', 'eu', 'também', 'só', 'pelo', 'pela', 'até', 'isso', 'ela', 'entre', 'era', 'depois', 'sem', 'mesmo', 'aos', 'ter', 'seus', 'suas', 'numa', 'nem', 'suas', 'meu', 'às', 'minha', 'têm', 'numa', 'pelos', 'pelas'];
    const spanishWords = ['el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para', 'al', 'del', 'los', 'las', 'una', 'como', 'pero', 'sus', 'le', 'ha', 'me', 'si', 'sin', 'sobre', 'este', 'ya', 'entre', 'cuando', 'todo', 'esta', 'ser', 'son', 'dos', 'también', 'fue', 'había', 'era', 'muy', 'años', 'hasta', 'desde', 'está', 'mi', 'porque', 'qué', 'sólo', 'han', 'yo', 'hay', 'vez', 'puede', 'todos', 'así', 'nos', 'ni', 'parte', 'tiene', 'él', 'uno', 'donde', 'bien', 'tiempo', 'mismo', 'ese', 'ahora', 'cada', 'e', 'vida', 'otro', 'después', 'te', 'otros', 'aunque', 'esa', 'eso', 'hace', 'otra', 'gobierno', 'tan', 'durante', 'siempre', 'día', 'tanto', 'ella', 'tres', 'sí', 'dijo', 'sido', 'gran', 'país', 'según', 'menos', 'mundo', 'año', 'antes', 'estado', 'contra', 'sino', 'forma', 'caso', 'nada', 'hacer', 'general', 'estaba', 'poco', 'estos', 'presidente', 'mayor', 'y', 'guerra', 'días', 'podría', 'agua', 'más'];
    const englishWords = ['the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'she', 'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what', 'so', 'up', 'out', 'if', 'about', 'who', 'get', 'which', 'go', 'me', 'when', 'make', 'can', 'like', 'time', 'no', 'just', 'him', 'know', 'take', 'people', 'into', 'year', 'your', 'good', 'some', 'could', 'them', 'see', 'other', 'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over', 'think', 'also', 'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first', 'well', 'way', 'even', 'new', 'want', 'because', 'any', 'these', 'give', 'day', 'most', 'us'];

    const words = text.toLowerCase().split(/\s+/);
    let ptScore = 0, esScore = 0, enScore = 0;

    words.forEach(word => {
      if (portugueseWords.includes(word)) ptScore++;
      if (spanishWords.includes(word)) esScore++;
      if (englishWords.includes(word)) enScore++;
    });

    if (ptScore > esScore && ptScore > enScore) return 'pt';
    if (esScore > ptScore && esScore > enScore) return 'es';
    return 'en';
  }

  async function processQuestion(question, resumes, language) {
    // Find the best matching resume for the detected language
    let resumeText = resumes[language];
    
    // Fallback to any available resume if the detected language is not available
    if (!resumeText) {
      const availableLanguages = Object.keys(resumes);
      if (availableLanguages.length > 0) {
        resumeText = resumes[availableLanguages[0]];
        language = availableLanguages[0];
      } else {
        throw new Error('Nenhum currículo disponível');
      }
    }

    // Simple keyword matching and response generation
    const response = generateResponse(question, resumeText, language);
    return response;
  }

  function generateResponse(question, resumeText, language) {
    const lowerQuestion = question.toLowerCase();
    const lowerResume = resumeText.toLowerCase();

    // Define response templates for different languages
    const templates = {
      pt: {
        notFound: 'Esta informação não está no seu currículo.',
        found: 'Com base no seu currículo: '
      },
      en: {
        notFound: 'This information is not in your resume.',
        found: 'Based on your resume: '
      },
      es: {
        notFound: 'Esta información no está en tu currículum.',
        found: 'Basado en tu currículum: '
      }
    };

    const template = templates[language] || templates.en;

    // Keywords for different types of questions
    const keywords = {
      experience: ['experiência', 'experience', 'experiencia', 'trabalho', 'work', 'trabajo', 'emprego', 'job', 'cargo', 'position', 'posición'],
      education: ['educação', 'education', 'educación', 'formação', 'formation', 'formación', 'universidade', 'university', 'universidad', 'curso', 'course', 'degree'],
      skills: ['habilidades', 'skills', 'habilidades', 'competências', 'competencies', 'competencias', 'conhecimento', 'knowledge', 'conocimiento'],
      contact: ['contato', 'contact', 'contacto', 'telefone', 'phone', 'teléfono', 'email', 'endereço', 'address', 'dirección'],
      name: ['nome', 'name', 'nombre']
    };

    // Try to find relevant information
    let foundInfo = '';
    let hasMatch = false;

    // Check for each category
    for (const [category, categoryKeywords] of Object.entries(keywords)) {
      if (categoryKeywords.some(keyword => lowerQuestion.includes(keyword))) {
        // Look for relevant sections in the resume
        const sentences = resumeText.split(/[.!?]+/);
        const relevantSentences = sentences.filter(sentence => 
          categoryKeywords.some(keyword => sentence.toLowerCase().includes(keyword)) ||
          sentence.toLowerCase().includes(lowerQuestion.split(' ').find(word => word.length > 3))
        );

        if (relevantSentences.length > 0) {
          foundInfo = relevantSentences.slice(0, 3).join('. ').trim();
          hasMatch = true;
          break;
        }
      }
    }

    // If no specific match, try general search
    if (!hasMatch) {
      const questionWords = lowerQuestion.split(' ').filter(word => word.length > 3);
      const sentences = resumeText.split(/[.!?]+/);
      
      for (const word of questionWords) {
        const matchingSentences = sentences.filter(sentence => 
          sentence.toLowerCase().includes(word)
        );
        
        if (matchingSentences.length > 0) {
          foundInfo = matchingSentences.slice(0, 2).join('. ').trim();
          hasMatch = true;
          break;
        }
      }
    }

    if (hasMatch && foundInfo) {
      return template.found + foundInfo + '.';
    } else {
      return template.notFound;
    }
  }

  function showResponse(response) {
    responseArea.innerHTML = response;
    responseArea.classList.remove('empty', 'error');
  }

  function showError(message) {
    responseArea.innerHTML = `<div class="error">${message}</div>`;
    responseArea.classList.add('error');
    responseArea.classList.remove('empty');
  }

  // Initialize
  responseArea.innerHTML = 'Faça uma pergunta sobre o seu currículo...';
  responseArea.classList.add('empty');
});

