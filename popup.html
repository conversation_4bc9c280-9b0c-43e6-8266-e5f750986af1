<!DOCTYPE html>
<html>
<head>
  <title>Resume Assistant</title>
  <link rel="stylesheet" href="popup.css">
  <meta charset="UTF-8">
</head>
<body>
  <div class="container">
    <header class="header">
      <h1>Resume Assistant</h1>
      <p class="subtitle">AI-powered job application assistant</p>
    </header>

    <!-- Language Selection Tabs -->
    <div class="language-tabs">
      <button class="tab-button active" data-lang="pt">
        🇧🇷 Português (Brasil)
      </button>
      <button class="tab-button" data-lang="en">
        🇺🇸 English
      </button>
    </div>

    <!-- Main Content -->
    <div class="main-content">
      <div class="question-section">
        <label for="question" class="question-label">
          <span id="questionLabelText">Digite sua pergunta sobre a vaga:</span>
        </label>
        <textarea
          id="question"
          class="question-input"
          placeholder="Ex: Descreva sua experiência com desenvolvimento web..."
          rows="4"
        ></textarea>
      </div>

      <button id="responderButton" class="responder-button">
        <span id="buttonText">Responder</span>
        <div id="loadingSpinner" class="spinner hidden"></div>
      </button>

      <div id="responseSection" class="response-section hidden">
        <label for="responseText" class="response-label">
          <span id="responseLabelText">Resposta gerada:</span>
        </label>
        <textarea
          id="responseText"
          class="response-textarea"
          readonly
          rows="5"
        ></textarea>
        <div class="response-actions">
          <button id="copyButton" class="copy-button">
            <span id="copyButtonText">Copiar</span>
          </button>
          <button id="clearButton" class="clear-button">
            <span id="clearButtonText">Limpar</span>
          </button>
        </div>
      </div>
    </div>

    <!-- Footer -->
    <footer class="footer">
      <a href="options.html" target="_blank" class="settings-link">
        <span id="settingsLinkText">⚙️ Gerenciar Currículos</span>
      </a>
    </footer>
  </div>

  <script src="openai-service.js"></script>
  <script src="popup.js"></script>
</body>
</html>

