<!DOCTYPE html>
<html>
<head>
  <title>Resume Assistant - Configuration</title>
  <link rel="stylesheet" href="options.css">
  <meta charset="UTF-8">
</head>
<body>
  <div class="container">
    <header class="header">
      <h1>Resume Assistant Configuration</h1>
      <p class="subtitle">Configure your resumes and OpenAI API settings</p>
    </header>

    <!-- OpenAI API Configuration -->
    <div class="section api-section">
      <h2>🔑 OpenAI API Configuration</h2>
      <p class="description">
        Configure your OpenAI API key to enable AI-powered response generation.
        <a href="https://platform.openai.com/api-keys" target="_blank">Get your API key here</a>
      </p>
      <div class="input-group">
        <label for="apiKey">OpenAI API Key:</label>
        <input type="password" id="apiKey" placeholder="sk-..." />
        <button id="testApiButton" class="test-button">Test Connection</button>
      </div>
      <div id="apiStatus" class="status-message"></div>
    </div>

    <!-- Resume Management -->
    <div class="section">
      <h2>📄 Resume Management</h2>
      <p class="description">
        Upload your resume files or paste the text content. The assistant will use this information to generate responses for job applications.
      </p>

      <!-- Portuguese Resume -->
      <div class="resume-section">
        <h3>🇧🇷 Português (Brasil)</h3>
        <div class="upload-area">
          <div class="file-upload">
            <input type="file" id="resumePtFile" accept=".pdf,.txt,.doc,.docx" />
            <label for="resumePtFile" class="upload-label">
              📎 Upload PDF or Document
            </label>
            <span id="resumePtFileName" class="file-name"></span>
          </div>
          <div class="divider">OR</div>
          <textarea
            id="resumePt"
            placeholder="Cole o texto do seu currículo em português aqui..."
            rows="8"
          ></textarea>
          <div class="char-count">
            <span id="resumePtCount">0</span> characters
          </div>
        </div>

        <!-- Additional Career Details for Portuguese -->
        <div class="career-details">
          <h4>📝 Detalhes Adicionais da Carreira (Opcional)</h4>
          <p class="field-description">Estes campos opcionais ajudam a gerar respostas mais personalizadas para suas candidaturas.</p>

          <div class="career-field">
            <label for="careerChallengePt">Maior desafio da carreira:</label>
            <textarea
              id="careerChallengePt"
              placeholder="Descreva seu maior desafio profissional e como você o superou..."
              rows="4"
            ></textarea>
            <div class="char-count">
              <span id="careerChallengePtCount">0</span> characters
            </div>
          </div>

          <div class="career-field">
            <label for="jobMotivationPt">Por que estou procurando outro emprego:</label>
            <textarea
              id="jobMotivationPt"
              placeholder="Explique sua motivação para buscar novas oportunidades..."
              rows="4"
            ></textarea>
            <div class="char-count">
              <span id="jobMotivationPtCount">0</span> characters
            </div>
          </div>

          <div class="career-field">
            <label for="proudProjectPt">Projeto que tenho mais orgulho e por quê:</label>
            <textarea
              id="proudProjectPt"
              placeholder="Descreva seu projeto mais significativo e por que você tem orgulho dele..."
              rows="4"
            ></textarea>
            <div class="char-count">
              <span id="proudProjectPtCount">0</span> characters
            </div>
          </div>
        </div>
      </div>

      <!-- English Resume -->
      <div class="resume-section">
        <h3>🇺🇸 English</h3>
        <div class="upload-area">
          <div class="file-upload">
            <input type="file" id="resumeEnFile" accept=".pdf,.txt,.doc,.docx" />
            <label for="resumeEnFile" class="upload-label">
              📎 Upload PDF or Document
            </label>
            <span id="resumeEnFileName" class="file-name"></span>
          </div>
          <div class="divider">OR</div>
          <textarea
            id="resumeEn"
            placeholder="Paste your English resume text here..."
            rows="8"
          ></textarea>
          <div class="char-count">
            <span id="resumeEnCount">0</span> characters
          </div>
        </div>

        <!-- Additional Career Details for English -->
        <div class="career-details">
          <h4>📝 Additional Career Details (Optional)</h4>
          <p class="field-description">These optional fields help generate more personalized responses for your job applications.</p>

          <div class="career-field">
            <label for="careerChallengeEn">Biggest career challenge:</label>
            <textarea
              id="careerChallengeEn"
              placeholder="Describe your biggest professional challenge and how you overcame it..."
              rows="4"
            ></textarea>
            <div class="char-count">
              <span id="careerChallengeEnCount">0</span> characters
            </div>
          </div>

          <div class="career-field">
            <label for="jobMotivationEn">Why I'm looking for a new job:</label>
            <textarea
              id="jobMotivationEn"
              placeholder="Explain your motivation for seeking new opportunities..."
              rows="4"
            ></textarea>
            <div class="char-count">
              <span id="jobMotivationEnCount">0</span> characters
            </div>
          </div>

          <div class="career-field">
            <label for="proudProjectEn">Project I'm most proud of and why:</label>
            <textarea
              id="proudProjectEn"
              placeholder="Describe your most meaningful professional achievement and why you're proud of it..."
              rows="4"
            ></textarea>
            <div class="char-count">
              <span id="proudProjectEnCount">0</span> characters
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Settings -->
    <div class="section">
      <h2>⚙️ Settings</h2>
      <div class="setting-item">
        <label class="checkbox-label">
          <input type="checkbox" id="strictMode" checked>
          <span class="checkmark"></span>
          Strict Mode (only use resume information for responses)
        </label>
      </div>
    </div>

    <!-- Action Buttons -->
    <div class="actions">
      <button id="saveButton" class="save-button">💾 Save Configuration</button>
      <button id="clearButton" class="clear-button">🗑️ Clear All Data</button>
    </div>

    <!-- Status Messages -->
    <div id="status" class="status"></div>
  </div>

  <script src="openai-service.js"></script>
  <script src="pdf-processor.js"></script>
  <script src="options.js"></script>
</body>
</html>

