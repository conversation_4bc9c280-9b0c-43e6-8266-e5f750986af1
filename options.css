body {
  font-family: Arial, sans-serif;
  margin: 0;
  padding: 20px;
  background-color: #f5f5f5;
  line-height: 1.6;
}

.container {
  max-width: 800px;
  margin: 0 auto;
  background-color: white;
  padding: 30px;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

h1 {
  color: #333;
  text-align: center;
  margin-bottom: 30px;
  font-size: 28px;
}

h2 {
  color: #555;
  margin-bottom: 15px;
  font-size: 20px;
  border-bottom: 2px solid #4CAF50;
  padding-bottom: 5px;
}

.section {
  margin-bottom: 30px;
}

.section p {
  color: #666;
  margin-bottom: 10px;
}

.section ul {
  color: #666;
  margin-left: 20px;
}

.section ul li {
  margin-bottom: 5px;
}

textarea {
  width: 100%;
  height: 200px;
  padding: 15px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
  font-family: 'Courier New', monospace;
  resize: vertical;
  box-sizing: border-box;
}

textarea:focus {
  outline: none;
  border-color: #4CAF50;
  box-shadow: 0 0 5px rgba(76, 175, 80, 0.3);
}

label {
  display: flex;
  align-items: center;
  font-size: 16px;
  color: #555;
  cursor: pointer;
}

label input[type="checkbox"] {
  margin-right: 10px;
  transform: scale(1.2);
}

.buttons {
  text-align: center;
  margin-top: 30px;
}

.buttons button {
  padding: 12px 30px;
  margin: 0 10px;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

#saveButton {
  background-color: #4CAF50;
  color: white;
}

#saveButton:hover {
  background-color: #45a049;
}

#saveButton:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

#clearButton {
  background-color: #f44336;
  color: white;
}

#clearButton:hover {
  background-color: #da190b;
}

.status {
  margin-top: 20px;
  padding: 15px;
  border-radius: 4px;
  text-align: center;
  font-weight: bold;
}

.status.success {
  background-color: #d4edda;
  color: #155724;
  border: 1px solid #c3e6cb;
}

.status.error {
  background-color: #f8d7da;
  color: #721c24;
  border: 1px solid #f5c6cb;
}

.status.info {
  background-color: #d1ecf1;
  color: #0c5460;
  border: 1px solid #bee5eb;
}

.char-count {
  text-align: right;
  font-size: 12px;
  color: #666;
  margin-top: 5px;
}

