/**
 * PDF Processor for Resume Assistant
 * Handles PDF file reading and text extraction
 */

class PDFProcessor {
  constructor() {
    this.supportedTypes = [
      'application/pdf',
      'text/plain',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document'
    ];
  }

  /**
   * Check if file type is supported
   * @param {File} file - File object
   * @returns {boolean} True if supported
   */
  isSupported(file) {
    return this.supportedTypes.includes(file.type) || 
           file.name.toLowerCase().endsWith('.pdf') ||
           file.name.toLowerCase().endsWith('.txt') ||
           file.name.toLowerCase().endsWith('.doc') ||
           file.name.toLowerCase().endsWith('.docx');
  }

  /**
   * Extract text from file
   * @param {File} file - File object
   * @returns {Promise<string>} Extracted text
   */
  async extractText(file) {
    if (!this.isSupported(file)) {
      throw new Error('Unsupported file type. Please use PDF, TXT, DOC, or DOCX files.');
    }

    try {
      if (file.type === 'text/plain' || file.name.toLowerCase().endsWith('.txt')) {
        return await this.extractTextFromTxt(file);
      } else if (file.type === 'application/pdf' || file.name.toLowerCase().endsWith('.pdf')) {
        return await this.extractTextFromPDF(file);
      } else {
        // For DOC/DOCX files, we'll try to read as text (limited support)
        return await this.extractTextFromDoc(file);
      }
    } catch (error) {
      console.error('Error extracting text from file:', error);
      throw new Error('Failed to extract text from file. Please try copying and pasting the text manually.');
    }
  }

  /**
   * Extract text from TXT file
   * @param {File} file - TXT file
   * @returns {Promise<string>} Text content
   */
  async extractTextFromTxt(file) {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => resolve(e.target.result);
      reader.onerror = (e) => reject(new Error('Failed to read text file'));
      reader.readAsText(file);
    });
  }

  /**
   * Extract text from PDF file using PDF.js
   * @param {File} file - PDF file
   * @returns {Promise<string>} Extracted text
   */
  async extractTextFromPDF(file) {
    // Note: This is a simplified PDF text extraction
    // For production use, you would want to include PDF.js library
    // For now, we'll provide a fallback message
    throw new Error('PDF text extraction requires additional libraries. Please copy and paste your resume text manually, or convert your PDF to a text file.');
  }

  /**
   * Extract text from DOC/DOCX file
   * @param {File} file - DOC/DOCX file
   * @returns {Promise<string>} Extracted text
   */
  async extractTextFromDoc(file) {
    // Note: This is a simplified approach
    // For production use, you would want to include libraries like mammoth.js for DOCX
    throw new Error('DOC/DOCX text extraction requires additional libraries. Please copy and paste your resume text manually, or convert your document to a text file.');
  }

  /**
   * Validate extracted text
   * @param {string} text - Extracted text
   * @returns {boolean} True if valid
   */
  validateText(text) {
    if (!text || typeof text !== 'string') {
      return false;
    }

    const trimmedText = text.trim();
    
    // Check minimum length
    if (trimmedText.length < 50) {
      return false;
    }

    // Check if it contains some common resume keywords
    const resumeKeywords = [
      'experience', 'education', 'skills', 'work', 'university', 'college',
      'experiência', 'educação', 'habilidades', 'trabalho', 'universidade',
      'formação', 'competências', 'conhecimento'
    ];

    const lowerText = trimmedText.toLowerCase();
    const hasResumeKeywords = resumeKeywords.some(keyword => 
      lowerText.includes(keyword)
    );

    return hasResumeKeywords;
  }

  /**
   * Clean and format extracted text
   * @param {string} text - Raw extracted text
   * @returns {string} Cleaned text
   */
  cleanText(text) {
    if (!text) return '';

    return text
      // Remove excessive whitespace
      .replace(/\s+/g, ' ')
      // Remove special characters that might interfere
      .replace(/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/g, '')
      // Normalize line breaks
      .replace(/\r\n/g, '\n')
      .replace(/\r/g, '\n')
      // Remove multiple consecutive line breaks
      .replace(/\n{3,}/g, '\n\n')
      // Trim
      .trim();
  }

  /**
   * Get file size in human readable format
   * @param {number} bytes - File size in bytes
   * @returns {string} Formatted size
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * Validate file before processing
   * @param {File} file - File to validate
   * @returns {Object} Validation result
   */
  validateFile(file) {
    const maxSize = 10 * 1024 * 1024; // 10MB
    
    if (!file) {
      return { valid: false, error: 'No file selected' };
    }

    if (file.size > maxSize) {
      return { 
        valid: false, 
        error: `File too large. Maximum size is ${this.formatFileSize(maxSize)}` 
      };
    }

    if (!this.isSupported(file)) {
      return { 
        valid: false, 
        error: 'Unsupported file type. Please use PDF, TXT, DOC, or DOCX files.' 
      };
    }

    return { valid: true };
  }
}

// Export for use in other files
if (typeof module !== 'undefined' && module.exports) {
  module.exports = PDFProcessor;
} else {
  window.PDFProcessor = PDFProcessor;
}
