body {
  width: 350px;
  min-height: 400px;
  margin: 0;
  padding: 0;
  font-family: Arial, sans-serif;
  background-color: #f5f5f5;
}

.container {
  padding: 20px;
}

h1 {
  text-align: center;
  color: #333;
  margin-bottom: 20px;
  font-size: 18px;
}

.language-selector {
  margin-bottom: 15px;
}

.language-selector label {
  display: block;
  margin-bottom: 5px;
  font-weight: bold;
  color: #555;
}

.language-selector select {
  width: 100%;
  padding: 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

#question {
  width: 100%;
  height: 80px;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  resize: vertical;
  font-size: 14px;
  margin-bottom: 15px;
  box-sizing: border-box;
}

#askButton {
  width: 100%;
  padding: 12px;
  background-color: #4CAF50;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 16px;
  margin-bottom: 15px;
}

#askButton:hover {
  background-color: #45a049;
}

#askButton:disabled {
  background-color: #cccccc;
  cursor: not-allowed;
}

.response-area {
  min-height: 100px;
  max-height: 200px;
  overflow-y: auto;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  background-color: white;
  margin-bottom: 15px;
  font-size: 14px;
  line-height: 1.4;
}

.response-area.empty {
  color: #999;
  font-style: italic;
}

a {
  display: block;
  text-align: center;
  color: #007cba;
  text-decoration: none;
  font-size: 14px;
}

a:hover {
  text-decoration: underline;
}

.loading {
  text-align: center;
  color: #666;
  font-style: italic;
}

.error {
  color: #d32f2f;
  background-color: #ffebee;
  padding: 10px;
  border-radius: 4px;
  border-left: 4px solid #d32f2f;
}

