document.addEventListener('DOMContentLoaded', function() {
  const resumeEn = document.getElementById('resumeEn');
  const resumePt = document.getElementById('resumePt');
  const resumeEs = document.getElementById('resumeEs');
  const strictMode = document.getElementById('strictMode');
  const saveButton = document.getElementById('saveButton');
  const clearButton = document.getElementById('clearButton');
  const status = document.getElementById('status');

  // Load saved data
  loadSavedData();

  // Add character counters
  addCharacterCounters();

  // Event listeners
  saveButton.addEventListener('click', saveResumes);
  clearButton.addEventListener('click', clearAllResumes);

  function loadSavedData() {
    chrome.storage.local.get(['resumes', 'settings'], function(result) {
      if (result.resumes) {
        resumeEn.value = result.resumes.en || '';
        resumePt.value = result.resumes.pt || '';
        resumeEs.value = result.resumes.es || '';
      }
      
      if (result.settings) {
        strictMode.checked = result.settings.strict_mode !== false;
      }
      
      updateCharacterCounts();
    });
  }

  function addCharacterCounters() {
    const textareas = [resumeEn, resumePt, resumeEs];
    
    textareas.forEach(textarea => {
      const counter = document.createElement('div');
      counter.className = 'char-count';
      textarea.parentNode.appendChild(counter);
      
      textarea.addEventListener('input', updateCharacterCounts);
    });
  }

  function updateCharacterCounts() {
    const textareas = [
      { element: resumeEn, name: 'Inglês' },
      { element: resumePt, name: 'Português' },
      { element: resumeEs, name: 'Espanhol' }
    ];
    
    textareas.forEach(({ element, name }) => {
      const counter = element.parentNode.querySelector('.char-count');
      const count = element.value.length;
      counter.textContent = `${count} caracteres`;
      
      if (count > 10000) {
        counter.style.color = '#f44336';
      } else if (count > 5000) {
        counter.style.color = '#ff9800';
      } else {
        counter.style.color = '#666';
      }
    });
  }

  function saveResumes() {
    const resumes = {
      en: resumeEn.value.trim(),
      pt: resumePt.value.trim(),
      es: resumeEs.value.trim()
    };

    const settings = {
      strict_mode: strictMode.checked,
      response_lang: 'auto'
    };

    // Validate that at least one resume is provided
    const hasContent = Object.values(resumes).some(resume => resume.length > 0);
    
    if (!hasContent) {
      showStatus('Por favor, adicione pelo menos um currículo antes de salvar.', 'error');
      return;
    }

    saveButton.disabled = true;
    saveButton.textContent = 'Salvando...';

    chrome.storage.local.set({ resumes, settings }, function() {
      if (chrome.runtime.lastError) {
        showStatus('Erro ao salvar: ' + chrome.runtime.lastError.message, 'error');
      } else {
        showStatus('Currículos salvos com sucesso!', 'success');
        
        // Show summary of what was saved
        const summary = [];
        if (resumes.en) summary.push('Inglês');
        if (resumes.pt) summary.push('Português');
        if (resumes.es) summary.push('Espanhol');
        
        setTimeout(() => {
          showStatus(`Currículos salvos em: ${summary.join(', ')}`, 'info');
        }, 2000);
      }
      
      saveButton.disabled = false;
      saveButton.textContent = 'Salvar Currículos';
    });
  }

  function clearAllResumes() {
    if (confirm('Tem certeza de que deseja limpar todos os currículos? Esta ação não pode ser desfeita.')) {
      resumeEn.value = '';
      resumePt.value = '';
      resumeEs.value = '';
      
      chrome.storage.local.remove(['resumes'], function() {
        showStatus('Todos os currículos foram removidos.', 'info');
        updateCharacterCounts();
      });
    }
  }

  function showStatus(message, type) {
    status.textContent = message;
    status.className = `status ${type}`;
    status.style.display = 'block';
    
    // Auto-hide after 5 seconds for success/info messages
    if (type === 'success' || type === 'info') {
      setTimeout(() => {
        status.style.display = 'none';
      }, 5000);
    }
  }

  // Auto-save functionality (optional)
  let autoSaveTimeout;
  
  function scheduleAutoSave() {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
      const resumes = {
        en: resumeEn.value.trim(),
        pt: resumePt.value.trim(),
        es: resumeEs.value.trim()
      };
      
      const hasContent = Object.values(resumes).some(resume => resume.length > 0);
      
      if (hasContent) {
        chrome.storage.local.set({ resumes }, function() {
          if (!chrome.runtime.lastError) {
            showStatus('Rascunho salvo automaticamente', 'info');
          }
        });
      }
    }, 30000); // Auto-save after 30 seconds of inactivity
  }

  // Add auto-save listeners
  [resumeEn, resumePt, resumeEs].forEach(textarea => {
    textarea.addEventListener('input', scheduleAutoSave);
  });
});

