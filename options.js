// Initialize services
const openAIService = new OpenAIService();
const pdfProcessor = new PDFProcessor();

document.addEventListener("DOMContentLoaded", async function () {
  // DOM elements
  const resumeEn = document.getElementById("resumeEn");
  const resumePt = document.getElementById("resumePt");
  const strictMode = document.getElementById("strictMode");
  const saveButton = document.getElementById("saveButton");
  const clearButton = document.getElementById("clearButton");
  const status = document.getElementById("status");

  // API configuration elements
  const apiKey = document.getElementById("apiKey");
  const testApiButton = document.getElementById("testApiButton");
  const apiStatus = document.getElementById("apiStatus");

  // File upload elements
  const resumePtFile = document.getElementById("resumePtFile");
  const resumeEnFile = document.getElementById("resumeEnFile");
  const resumePtFileName = document.getElementById("resumePtFileName");
  const resumeEnFileName = document.getElementById("resumeEnFileName");

  // Character counters
  const resumePtCount = document.getElementById("resumePtCount");
  const resumeEnCount = document.getElementById("resumeEnCount");

  // Initialize
  await loadSavedData();
  updateCharacterCounts();

  // Event listeners
  saveButton.addEventListener("click", saveConfiguration);
  clearButton.addEventListener("click", clearAllData);
  testApiButton.addEventListener("click", testApiConnection);

  // File upload listeners
  resumePtFile.addEventListener("change", (e) => handleFileUpload(e, "pt"));
  resumeEnFile.addEventListener("change", (e) => handleFileUpload(e, "en"));

  // Text input listeners
  resumePt.addEventListener("input", updateCharacterCounts);
  resumeEn.addEventListener("input", updateCharacterCounts);

  async function loadSavedData() {
    // Load OpenAI API key
    await openAIService.loadApiKey();
    if (openAIService.isConfigured()) {
      apiKey.value = "••••••••••••••••"; // Mask the API key
      showApiStatus("API key loaded", "success");
    }

    // Load resumes and settings
    chrome.storage.local.get(["resumes", "settings"], function (result) {
      if (result.resumes) {
        resumeEn.value = result.resumes.en || "";
        resumePt.value = result.resumes.pt || "";
      }

      if (result.settings) {
        strictMode.checked = result.settings.strict_mode !== false;
      }

      updateCharacterCounts();
    });
  }

  async function handleFileUpload(event, language) {
    const file = event.target.files[0];
    if (!file) return;

    const validation = pdfProcessor.validateFile(file);
    if (!validation.valid) {
      showStatus(validation.error, "error");
      event.target.value = ""; // Clear the input
      return;
    }

    const fileNameElement =
      language === "pt" ? resumePtFileName : resumeEnFileName;
    const textArea = language === "pt" ? resumePt : resumeEn;

    try {
      showStatus(`Processing ${file.name}...`, "info");
      fileNameElement.textContent = `📄 ${
        file.name
      } (${pdfProcessor.formatFileSize(file.size)})`;

      const extractedText = await pdfProcessor.extractText(file);
      const cleanedText = pdfProcessor.cleanText(extractedText);

      if (!pdfProcessor.validateText(cleanedText)) {
        throw new Error(
          "The extracted text does not appear to be a valid resume. Please check the file content."
        );
      }

      textArea.value = cleanedText;
      updateCharacterCounts();
      showStatus(`Successfully processed ${file.name}`, "success");
    } catch (error) {
      console.error("File processing error:", error);
      showStatus(error.message, "error");
      fileNameElement.textContent = "";
      event.target.value = ""; // Clear the input
    }
  }

  async function testApiConnection() {
    const apiKeyValue = apiKey.value.trim();

    if (!apiKeyValue || apiKeyValue === "••••••••••••••••") {
      showApiStatus("Please enter your OpenAI API key", "error");
      return;
    }

    testApiButton.disabled = true;
    testApiButton.textContent = "Testing...";
    showApiStatus("Testing connection...", "info");

    try {
      await openAIService.initialize(apiKeyValue);
      const isConnected = await openAIService.testConnection();

      if (isConnected) {
        showApiStatus("✅ API connection successful!", "success");
        apiKey.value = "••••••••••••••••"; // Mask the key after successful test
      } else {
        showApiStatus(
          "❌ API connection failed. Please check your API key.",
          "error"
        );
      }
    } catch (error) {
      console.error("API test error:", error);
      showApiStatus(`❌ Error: ${error.message}`, "error");
    } finally {
      testApiButton.disabled = false;
      testApiButton.textContent = "Test Connection";
    }
  }

  function showApiStatus(message, type) {
    apiStatus.textContent = message;
    apiStatus.className = `status-message ${type}`;

    if (type === "success" || type === "info") {
      setTimeout(() => {
        apiStatus.textContent = "";
        apiStatus.className = "status-message";
      }, 5000);
    }
  }

  function updateCharacterCounts() {
    // Update Portuguese counter
    const ptCount = resumePt.value.length;
    resumePtCount.textContent = ptCount;

    // Update English counter
    const enCount = resumeEn.value.length;
    resumeEnCount.textContent = enCount;

    // Color coding for character counts
    [
      { element: resumePtCount, count: ptCount },
      { element: resumeEnCount, count: enCount },
    ].forEach(({ element, count }) => {
      if (count > 10000) {
        element.style.color = "#e74c3c";
      } else if (count > 5000) {
        element.style.color = "#f39c12";
      } else {
        element.style.color = "#7f8c8d";
      }
    });
  }

  async function saveConfiguration() {
    const resumes = {
      en: resumeEn.value.trim(),
      pt: resumePt.value.trim(),
    };

    const settings = {
      strict_mode: strictMode.checked,
    };

    // Validate that at least one resume is provided
    const hasContent = Object.values(resumes).some(
      (resume) => resume.length > 0
    );

    if (!hasContent) {
      showStatus("Please add at least one resume before saving.", "error");
      return;
    }

    // Validate API key if provided
    const apiKeyValue = apiKey.value.trim();
    if (apiKeyValue && apiKeyValue !== "••••••••••••••••") {
      try {
        await openAIService.initialize(apiKeyValue);
        showStatus("Configuration saved successfully!", "success");
      } catch (error) {
        showStatus("Invalid API key. Please check and try again.", "error");
        return;
      }
    }

    saveButton.disabled = true;
    saveButton.textContent = "💾 Saving...";

    chrome.storage.local.set({ resumes, settings }, function () {
      if (chrome.runtime.lastError) {
        showStatus(
          "Error saving: " + chrome.runtime.lastError.message,
          "error"
        );
      } else {
        showStatus("✅ Configuration saved successfully!", "success");

        // Show summary of what was saved
        const summary = [];
        if (resumes.en) summary.push("English");
        if (resumes.pt) summary.push("Portuguese");

        setTimeout(() => {
          showStatus(`Resumes saved: ${summary.join(", ")}`, "info");
        }, 2000);
      }

      saveButton.disabled = false;
      saveButton.textContent = "💾 Save Configuration";
    });
  }

  function clearAllData() {
    if (
      confirm(
        "Are you sure you want to clear all data? This includes resumes and API key. This action cannot be undone."
      )
    ) {
      // Clear form fields
      resumeEn.value = "";
      resumePt.value = "";
      apiKey.value = "";

      // Clear file names
      resumePtFileName.textContent = "";
      resumeEnFileName.textContent = "";

      // Clear storage
      chrome.storage.local.clear(function () {
        showStatus("🗑️ All data has been cleared.", "info");
        showApiStatus("", "");
        updateCharacterCounts();
      });
    }
  }

  function showStatus(message, type) {
    status.textContent = message;
    status.className = `status ${type}`;
    status.style.display = "block";

    // Auto-hide after 5 seconds for success/info messages
    if (type === "success" || type === "info") {
      setTimeout(() => {
        status.style.display = "none";
      }, 5000);
    }
  }

  // Auto-save functionality (optional)
  let autoSaveTimeout;

  function scheduleAutoSave() {
    clearTimeout(autoSaveTimeout);
    autoSaveTimeout = setTimeout(() => {
      const resumes = {
        en: resumeEn.value.trim(),
        pt: resumePt.value.trim(),
      };

      const hasContent = Object.values(resumes).some(
        (resume) => resume.length > 0
      );

      if (hasContent) {
        chrome.storage.local.set({ resumes }, function () {
          if (!chrome.runtime.lastError) {
            showStatus("📝 Draft saved automatically", "info");
          }
        });
      }
    }, 30000); // Auto-save after 30 seconds of inactivity
  }

  // Add auto-save listeners
  [resumeEn, resumePt].forEach((textarea) => {
    textarea.addEventListener("input", scheduleAutoSave);
  });
});
