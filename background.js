// Background script for Resume Assistant Extension

chrome.runtime.onInstalled.addListener(function (details) {
  if (details.reason === "install") {
    // Set default settings on first install
    chrome.storage.local.set({
      settings: {
        strict_mode: true,
        response_lang: "auto",
      },
    });

    // Open options page on first install
    chrome.tabs.create({
      url: chrome.runtime.getURL("options.html"),
    });
  }
});
