// Background script for Resume Assistant Extension

chrome.runtime.onInstalled.addListener(function(details) {
  if (details.reason === 'install') {
    // Set default settings on first install
    chrome.storage.local.set({
      settings: {
        strict_mode: true,
        response_lang: 'auto'
      }
    });
    
    // Open options page on first install
    chrome.tabs.create({
      url: chrome.runtime.getURL('options.html')
    });
  }
});

// Handle extension icon click
chrome.action.onClicked.addListener(function(tab) {
  // This will open the popup, but we can add additional logic here if needed
});

// Listen for messages from content scripts or popup
chrome.runtime.onMessage.addListener(function(request, sender, sendResponse) {
  if (request.action === 'getResumes') {
    chrome.storage.local.get(['resumes', 'settings'], function(result) {
      sendResponse({
        resumes: result.resumes || {},
        settings: result.settings || { strict_mode: true, response_lang: 'auto' }
      });
    });
    return true; // Will respond asynchronously
  }
  
  if (request.action === 'saveResumes') {
    chrome.storage.local.set({
      resumes: request.resumes,
      settings: request.settings
    }, function() {
      sendResponse({ success: !chrome.runtime.lastError });
    });
    return true; // Will respond asynchronously
  }
});

// Handle storage changes
chrome.storage.onChanged.addListener(function(changes, namespace) {
  if (namespace === 'local') {
    console.log('Storage changed:', changes);
  }
});

// Utility function to validate resume data
function validateResumeData(resumes) {
  if (!resumes || typeof resumes !== 'object') {
    return false;
  }
  
  const validLanguages = ['en', 'pt', 'es'];
  const hasValidResume = validLanguages.some(lang => 
    resumes[lang] && typeof resumes[lang] === 'string' && resumes[lang].trim().length > 0
  );
  
  return hasValidResume;
}

// Export utility functions for testing (if needed)
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    validateResumeData
  };
}

