import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button.jsx'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card.jsx'
import { Textarea } from '@/components/ui/textarea.jsx'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select.jsx'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs.jsx'
import { Label } from '@/components/ui/label.jsx'
import { Badge } from '@/components/ui/badge.jsx'
import { AlertCircle, MessageSquare, Settings, FileText, Languages } from 'lucide-react'
import './App.css'

function App() {
  const [resumes, setResumes] = useState({
    en: '',
    pt: '',
    es: ''
  })
  const [question, setQuestion] = useState('')
  const [response, setResponse] = useState('')
  const [selectedLanguage, setSelectedLanguage] = useState('auto')
  const [isLoading, setIsLoading] = useState(false)
  const [strictMode, setStrictMode] = useState(true)

  // Load data from localStorage on component mount
  useEffect(() => {
    const savedResumes = localStorage.getItem('resumeAssistant_resumes')
    const savedSettings = localStorage.getItem('resumeAssistant_settings')
    
    if (savedResumes) {
      setResumes(JSON.parse(savedResumes))
    }
    
    if (savedSettings) {
      const settings = JSON.parse(savedSettings)
      setStrictMode(settings.strict_mode !== false)
      setSelectedLanguage(settings.response_lang || 'auto')
    }
  }, [])

  // Save resumes to localStorage
  const saveResumes = () => {
    localStorage.setItem('resumeAssistant_resumes', JSON.stringify(resumes))
    localStorage.setItem('resumeAssistant_settings', JSON.stringify({
      strict_mode: strictMode,
      response_lang: selectedLanguage
    }))
    alert('Currículos salvos com sucesso!')
  }

  // Clear all resumes
  const clearResumes = () => {
    if (confirm('Tem certeza de que deseja limpar todos os currículos?')) {
      setResumes({ en: '', pt: '', es: '' })
      localStorage.removeItem('resumeAssistant_resumes')
      alert('Currículos removidos com sucesso!')
    }
  }

  // Language detection function
  const detectLanguage = (text) => {
    const portugueseWords = ['o', 'a', 'de', 'que', 'e', 'do', 'da', 'em', 'um', 'para', 'é', 'com', 'não', 'uma', 'os', 'no', 'se', 'na', 'por', 'mais', 'as', 'dos', 'como', 'mas', 'foi', 'ao', 'ele', 'das', 'tem', 'à', 'seu', 'sua', 'ou', 'ser', 'quando', 'muito', 'há', 'nos', 'já', 'está', 'eu', 'também', 'só', 'pelo', 'pela', 'até', 'isso', 'ela', 'entre', 'era', 'depois', 'sem', 'mesmo', 'aos', 'ter', 'seus', 'suas', 'numa', 'nem', 'suas', 'meu', 'às', 'minha', 'têm', 'numa', 'pelos', 'pelas']
    const spanishWords = ['el', 'la', 'de', 'que', 'y', 'a', 'en', 'un', 'es', 'se', 'no', 'te', 'lo', 'le', 'da', 'su', 'por', 'son', 'con', 'para', 'al', 'del', 'los', 'las', 'una', 'como', 'pero', 'sus', 'le', 'ha', 'me', 'si', 'sin', 'sobre', 'este', 'ya', 'entre', 'cuando', 'todo', 'esta', 'ser', 'son', 'dos', 'también', 'fue', 'había', 'era', 'muy', 'años', 'hasta', 'desde', 'está', 'mi', 'porque', 'qué', 'sólo', 'han', 'yo', 'hay', 'vez', 'puede', 'todos', 'así', 'nos', 'ni', 'parte', 'tiene', 'él', 'uno', 'donde', 'bien', 'tiempo', 'mismo', 'ese', 'ahora', 'cada', 'e', 'vida', 'otro', 'después', 'te', 'otros', 'aunque', 'esa', 'eso', 'hace', 'otra', 'gobierno', 'tan', 'durante', 'siempre', 'día', 'tanto', 'ella', 'tres', 'sí', 'dijo', 'sido', 'gran', 'país', 'según', 'menos', 'mundo', 'año', 'antes', 'estado', 'contra', 'sino', 'forma', 'caso', 'nada', 'hacer', 'general', 'estaba', 'poco', 'estos', 'presidente', 'mayor', 'y', 'guerra', 'días', 'podría', 'agua', 'más']
    const englishWords = ['the', 'be', 'to', 'of', 'and', 'a', 'in', 'that', 'have', 'i', 'it', 'for', 'not', 'on', 'with', 'he', 'as', 'you', 'do', 'at', 'this', 'but', 'his', 'by', 'from', 'they', 'she', 'or', 'an', 'will', 'my', 'one', 'all', 'would', 'there', 'their', 'what', 'so', 'up', 'out', 'if', 'about', 'who', 'get', 'which', 'go', 'me', 'when', 'make', 'can', 'like', 'time', 'no', 'just', 'him', 'know', 'take', 'people', 'into', 'year', 'your', 'good', 'some', 'could', 'them', 'see', 'other', 'than', 'then', 'now', 'look', 'only', 'come', 'its', 'over', 'think', 'also', 'back', 'after', 'use', 'two', 'how', 'our', 'work', 'first', 'well', 'way', 'even', 'new', 'want', 'because', 'any', 'these', 'give', 'day', 'most', 'us']

    const words = text.toLowerCase().split(/\s+/)
    let ptScore = 0, esScore = 0, enScore = 0

    words.forEach(word => {
      if (portugueseWords.includes(word)) ptScore++
      if (spanishWords.includes(word)) esScore++
      if (englishWords.includes(word)) enScore++
    })

    if (ptScore > esScore && ptScore > enScore) return 'pt'
    if (esScore > ptScore && esScore > enScore) return 'es'
    return 'en'
  }

  // Generate response based on question and resume
  const generateResponse = (question, resumeText, language) => {
    const lowerQuestion = question.toLowerCase()
    const lowerResume = resumeText.toLowerCase()

    const templates = {
      pt: {
        notFound: 'Esta informação não está no seu currículo.',
        found: 'Com base no seu currículo: '
      },
      en: {
        notFound: 'This information is not in your resume.',
        found: 'Based on your resume: '
      },
      es: {
        notFound: 'Esta información no está en tu currículum.',
        found: 'Basado en tu currículum: '
      }
    }

    const template = templates[language] || templates.en

    const keywords = {
      experience: ['experiência', 'experience', 'experiencia', 'trabalho', 'work', 'trabajo', 'emprego', 'job', 'cargo', 'position', 'posición'],
      education: ['educação', 'education', 'educación', 'formação', 'formation', 'formación', 'universidade', 'university', 'universidad', 'curso', 'course', 'degree'],
      skills: ['habilidades', 'skills', 'habilidades', 'competências', 'competencies', 'competencias', 'conhecimento', 'knowledge', 'conocimiento'],
      contact: ['contato', 'contact', 'contacto', 'telefone', 'phone', 'teléfono', 'email', 'endereço', 'address', 'dirección'],
      name: ['nome', 'name', 'nombre']
    }

    let foundInfo = ''
    let hasMatch = false

    for (const [category, categoryKeywords] of Object.entries(keywords)) {
      if (categoryKeywords.some(keyword => lowerQuestion.includes(keyword))) {
        const sentences = resumeText.split(/[.!?]+/)
        const relevantSentences = sentences.filter(sentence => 
          categoryKeywords.some(keyword => sentence.toLowerCase().includes(keyword)) ||
          sentence.toLowerCase().includes(lowerQuestion.split(' ').find(word => word.length > 3))
        )

        if (relevantSentences.length > 0) {
          foundInfo = relevantSentences.slice(0, 3).join('. ').trim()
          hasMatch = true
          break
        }
      }
    }

    if (!hasMatch) {
      const questionWords = lowerQuestion.split(' ').filter(word => word.length > 3)
      const sentences = resumeText.split(/[.!?]+/)
      
      for (const word of questionWords) {
        const matchingSentences = sentences.filter(sentence => 
          sentence.toLowerCase().includes(word)
        )
        
        if (matchingSentences.length > 0) {
          foundInfo = matchingSentences.slice(0, 2).join('. ').trim()
          hasMatch = true
          break
        }
      }
    }

    if (hasMatch && foundInfo) {
      return template.found + foundInfo + '.'
    } else {
      return template.notFound
    }
  }

  // Handle question submission
  const handleQuestion = async () => {
    if (!question.trim()) {
      alert('Por favor, digite uma pergunta.')
      return
    }

    const hasResumes = Object.values(resumes).some(resume => resume.trim().length > 0)
    if (!hasResumes) {
      alert('Nenhum currículo encontrado. Por favor, adicione seus currículos na aba de gerenciamento.')
      return
    }

    setIsLoading(true)
    
    try {
      const detectedLang = selectedLanguage === 'auto' ? detectLanguage(question) : selectedLanguage
      let resumeText = resumes[detectedLang]
      
      if (!resumeText) {
        const availableLanguages = Object.keys(resumes).filter(lang => resumes[lang].trim())
        if (availableLanguages.length > 0) {
          resumeText = resumes[availableLanguages[0]]
        } else {
          throw new Error('Nenhum currículo disponível')
        }
      }

      const answer = generateResponse(question, resumeText, detectedLang)
      setResponse(answer)
    } catch (error) {
      setResponse('Erro ao processar a pergunta: ' + error.message)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <div className="max-w-4xl mx-auto">
        <div className="text-center mb-8">
          <div className="flex items-center justify-center gap-3 mb-4">
            <FileText className="h-8 w-8 text-blue-600" />
            <h1 className="text-4xl font-bold text-gray-900">Resume Assistant</h1>
          </div>
          <p className="text-lg text-gray-600">Assistente de Currículo Multilíngue com IA</p>
          <div className="flex justify-center gap-2 mt-4">
            <Badge variant="secondary" className="flex items-center gap-1">
              <Languages className="h-3 w-3" />
              Português
            </Badge>
            <Badge variant="secondary" className="flex items-center gap-1">
              <Languages className="h-3 w-3" />
              English
            </Badge>
            <Badge variant="secondary" className="flex items-center gap-1">
              <Languages className="h-3 w-3" />
              Español
            </Badge>
          </div>
        </div>

        <Tabs defaultValue="assistant" className="w-full">
          <TabsList className="grid w-full grid-cols-2">
            <TabsTrigger value="assistant" className="flex items-center gap-2">
              <MessageSquare className="h-4 w-4" />
              Assistente
            </TabsTrigger>
            <TabsTrigger value="manage" className="flex items-center gap-2">
              <Settings className="h-4 w-4" />
              Gerenciar Currículos
            </TabsTrigger>
          </TabsList>

          <TabsContent value="assistant" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  Faça uma Pergunta
                </CardTitle>
                <CardDescription>
                  Digite sua pergunta sobre o currículo. O assistente responderá apenas com informações disponíveis no seu currículo.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="language">Idioma da Resposta</Label>
                  <Select value={selectedLanguage} onValueChange={setSelectedLanguage}>
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">Detectar Automaticamente</SelectItem>
                      <SelectItem value="en">English</SelectItem>
                      <SelectItem value="pt">Português</SelectItem>
                      <SelectItem value="es">Español</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="question">Sua Pergunta</Label>
                  <Textarea
                    id="question"
                    placeholder="Ex: Qual é a minha experiência profissional? / What are my technical skills? / ¿Cuál es mi formación académica?"
                    value={question}
                    onChange={(e) => setQuestion(e.target.value)}
                    className="min-h-[100px]"
                  />
                </div>

                <Button 
                  onClick={handleQuestion} 
                  disabled={isLoading || !question.trim()}
                  className="w-full"
                >
                  {isLoading ? 'Processando...' : 'Perguntar'}
                </Button>

                {response && (
                  <Card className="mt-4">
                    <CardHeader>
                      <CardTitle className="text-lg">Resposta</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="text-gray-700 leading-relaxed">{response}</p>
                    </CardContent>
                  </Card>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="manage" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Settings className="h-5 w-5" />
                  Gerenciar Currículos
                </CardTitle>
                <CardDescription>
                  Adicione suas versões de currículo em diferentes idiomas. O assistente usará apenas as informações fornecidas aqui.
                </CardDescription>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="bg-blue-50 p-4 rounded-lg border border-blue-200">
                  <div className="flex items-start gap-3">
                    <AlertCircle className="h-5 w-5 text-blue-600 mt-0.5" />
                    <div>
                      <h4 className="font-semibold text-blue-900 mb-2">Instruções Importantes</h4>
                      <ul className="text-sm text-blue-800 space-y-1">
                        <li>• Cole o texto completo do seu currículo em cada idioma</li>
                        <li>• Certifique-se de incluir todas as informações relevantes</li>
                        <li>• O assistente responderá apenas com base no conteúdo fornecido</li>
                        <li>• Nunca inventará informações não declaradas no currículo</li>
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="space-y-4">
                  <div>
                    <Label htmlFor="resume-en">Currículo em Inglês</Label>
                    <Textarea
                      id="resume-en"
                      placeholder="Cole seu currículo em inglês aqui..."
                      value={resumes.en}
                      onChange={(e) => setResumes(prev => ({ ...prev, en: e.target.value }))}
                      className="min-h-[200px] mt-2"
                    />
                    <p className="text-sm text-gray-500 mt-1">{resumes.en.length} caracteres</p>
                  </div>

                  <div>
                    <Label htmlFor="resume-pt">Currículo em Português</Label>
                    <Textarea
                      id="resume-pt"
                      placeholder="Cole seu currículo em português aqui..."
                      value={resumes.pt}
                      onChange={(e) => setResumes(prev => ({ ...prev, pt: e.target.value }))}
                      className="min-h-[200px] mt-2"
                    />
                    <p className="text-sm text-gray-500 mt-1">{resumes.pt.length} caracteres</p>
                  </div>

                  <div>
                    <Label htmlFor="resume-es">Currículo em Espanhol</Label>
                    <Textarea
                      id="resume-es"
                      placeholder="Cole seu currículo em espanhol aqui..."
                      value={resumes.es}
                      onChange={(e) => setResumes(prev => ({ ...prev, es: e.target.value }))}
                      className="min-h-[200px] mt-2"
                    />
                    <p className="text-sm text-gray-500 mt-1">{resumes.es.length} caracteres</p>
                  </div>
                </div>

                <div className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    id="strict-mode"
                    checked={strictMode}
                    onChange={(e) => setStrictMode(e.target.checked)}
                    className="rounded"
                  />
                  <Label htmlFor="strict-mode">
                    Modo Estrito (responder apenas com informações do currículo)
                  </Label>
                </div>

                <div className="flex gap-4">
                  <Button onClick={saveResumes} className="flex-1">
                    Salvar Currículos
                  </Button>
                  <Button onClick={clearResumes} variant="destructive" className="flex-1">
                    Limpar Todos
                  </Button>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </div>
  )
}

export default App

